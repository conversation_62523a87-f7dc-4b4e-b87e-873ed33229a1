#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test rapide pour vérifier que la solution fonctionne avec vos données
"""

import pandas as pd
import numpy as np
import os
import sys

def test_fichiers():
    """
    Test pour trouver et charger vos fichiers de données
    """
    print("🔍 RECHERCHE DES FICHIERS DE DONNÉES")
    print("="*50)
    
    # Fichiers possibles
    possible_files = [
        "../pfe2.csv",
        "../pfe.csv",
        "../pfe1.csv", 
        "../123.csv",
        "pfe2.csv",
        "pfe.csv",
        "pfe1.csv",
        "123.csv"
    ]
    
    found_files = []
    
    for file_path in possible_files:
        if os.path.exists(file_path):
            try:
                # Essayer de charger le fichier
                df = pd.read_csv(file_path, sep="|", encoding='utf-8', decimal='.', nrows=5)
                found_files.append((file_path, len(df.columns), list(df.columns)))
                print(f"✅ {file_path}: {len(df.columns)} colonnes")
            except Exception as e:
                try:
                    # Essayer avec d'autres paramètres
                    df = pd.read_csv(file_path, nrows=5)
                    found_files.append((file_path, len(df.columns), list(df.columns)))
                    print(f"✅ {file_path}: {len(df.columns)} colonnes (séparateur standard)")
                except Exception as e2:
                    print(f"❌ {file_path}: Erreur - {str(e2)}")
    
    if not found_files:
        print("❌ Aucun fichier CSV trouvé ou lisible")
        return None
    
    # Choisir le fichier avec le plus de colonnes (probablement le bon)
    best_file = max(found_files, key=lambda x: x[1])
    print(f"\n🎯 Fichier sélectionné: {best_file[0]}")
    print(f"   Colonnes: {best_file[2]}")
    
    return best_file[0]

def test_chargement_complet(file_path):
    """
    Test de chargement complet du fichier
    """
    print(f"\n📊 CHARGEMENT COMPLET DE {file_path}")
    print("="*50)
    
    try:
        # Essayer avec les paramètres de votre projet original
        df = pd.read_csv(file_path, sep="|", encoding='utf-8', decimal='.')
        print(f"✅ Chargement réussi avec séparateur '|'")
    except:
        try:
            df = pd.read_csv(file_path, encoding='utf-8')
            print(f"✅ Chargement réussi avec séparateur standard")
        except Exception as e:
            print(f"❌ Erreur de chargement: {str(e)}")
            return None
    
    print(f"   • Nombre de lignes: {len(df):,}")
    print(f"   • Nombre de colonnes: {len(df.columns)}")
    print(f"   • Colonnes: {list(df.columns)}")
    
    # Vérifier les colonnes nécessaires
    required_cols = ['DLMP', 'ARPU', 'CTX', 'TYPE_CLIENT']
    missing_cols = [col for col in required_cols if col not in df.columns]
    
    if missing_cols:
        print(f"⚠️  Colonnes manquantes: {missing_cols}")
        print(f"   Colonnes disponibles: {list(df.columns)}")
        return None
    else:
        print(f"✅ Toutes les colonnes nécessaires sont présentes")
    
    # Vérifier les données B2C
    if 'TYPE_CLIENT' in df.columns:
        type_counts = df['TYPE_CLIENT'].value_counts()
        print(f"   • Types de clients: {dict(type_counts)}")
        
        if 'B2C' in type_counts:
            b2c_count = type_counts['B2C']
            print(f"   • Clients B2C: {b2c_count:,}")
            
            if b2c_count > 1000000:
                print(f"⚠️  ATTENTION: {b2c_count:,} clients B2C - c'est beaucoup!")
                print(f"   C'est exactement le problème de mémoire que nous résolvons.")
            
            return df
        else:
            print(f"❌ Pas de clients B2C trouvés")
            return None
    
    return df

def test_solution_simple(df):
    """
    Test simple de la solution sans classification hiérarchique
    """
    print(f"\n🧪 TEST SIMPLE DE LA SOLUTION")
    print("="*50)
    
    try:
        # Filtrer les clients B2C
        df_b2c = df[df['TYPE_CLIENT'] == 'B2C'][['DLMP', 'ARPU', 'CTX']].copy()
        print(f"✅ {len(df_b2c):,} clients B2C extraits")
        
        # Supprimer les valeurs négatives d'ARPU
        initial_size = len(df_b2c)
        df_b2c = df_b2c[df_b2c['ARPU'] >= 0]
        print(f"✅ {initial_size - len(df_b2c)} valeurs négatives supprimées")
        
        # Statistiques de base
        print(f"\n📊 Statistiques des données B2C:")
        print(df_b2c.describe())
        
        # Test de normalisation sur un échantillon
        from sklearn.preprocessing import StandardScaler
        from sklearn.model_selection import train_test_split
        
        # Prendre un échantillon pour le test
        sample_size = min(10000, len(df_b2c))
        df_sample = df_b2c.sample(n=sample_size, random_state=42)
        print(f"\n✅ Échantillon de {sample_size:,} clients pour le test")
        
        # Division et normalisation
        X_train, X_test = train_test_split(df_sample, test_size=0.4, random_state=42)
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        print(f"✅ Normalisation réussie")
        print(f"   • Train: {X_train_scaled.shape}")
        print(f"   • Test: {X_test_scaled.shape}")
        
        # Test K-means simple
        from sklearn.cluster import KMeans
        from sklearn.metrics import silhouette_score
        
        kmeans = KMeans(n_clusters=5, random_state=42, n_init=10)
        labels = kmeans.fit_predict(X_train_scaled)
        
        silhouette = silhouette_score(X_train_scaled, labels)
        print(f"✅ K-means réussi - Score silhouette: {silhouette:.3f}")
        
        print(f"\n🎉 TEST SIMPLE RÉUSSI!")
        print(f"✅ Vos données sont compatibles avec la solution")
        print(f"✅ La classification fonctionne correctement")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur dans le test simple: {str(e)}")
        return False

def main():
    """
    Fonction principale de test
    """
    print("🚀 TEST RAPIDE DE LA SOLUTION B2C")
    print("="*60)
    
    # Étape 1: Trouver les fichiers
    file_path = test_fichiers()
    if file_path is None:
        return False
    
    # Étape 2: Charger les données
    df = test_chargement_complet(file_path)
    if df is None:
        return False
    
    # Étape 3: Test simple
    success = test_solution_simple(df)
    
    if success:
        print(f"\n🎯 CONCLUSION:")
        print(f"✅ Vos données sont prêtes pour la solution complète")
        print(f"✅ Utilisez maintenant: python utiliser_solution_b2c.py")
        print(f"✅ Ou ouvrez le notebook: B2C_solution_notebook.ipynb")
    else:
        print(f"\n❌ PROBLÈME DÉTECTÉ:")
        print(f"   Vérifiez la structure de vos données")
        print(f"   Contactez-moi pour plus d'aide")
    
    return success

if __name__ == "__main__":
    main()
