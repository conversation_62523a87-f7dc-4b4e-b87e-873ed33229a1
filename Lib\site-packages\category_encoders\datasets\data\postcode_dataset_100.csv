index,postcode,HIER_postcode_1,HIER_postcode_2,HIER_postcode_3,HIER_postcode_4,target_binary,target_non_binary,target_categorical
0,S4 2FQ,S,S4,S4 2,S4 2F,1,1,dog
1,S9 2UE,S,S9,S9 2,S9 2U,1,1,dog
2,S4 6UA,S,S4,S4 6,S4 6U,1,1,dog
3,S6 3SA,S,S6,S6 3,S6 3S,1,1,dog
4,S8 3PT,S,S8,S8 3,S8 3P,1,1,dog
5,L1 7FW,L,L1,L1 7,L1 7F,1,1,dog
6,L1 3SF,L,L1,L1 3,L1 3S,1,1,dog
7,L6 8HJ,L,L6,L6 8,L6 8H,1,1,dog
8,L4 2UD,L,L4,L4 2,L4 2U,1,1,dog
9,L4 6PH,L,L4,L4 6,L4 6P,1,1,dog
10,S3 6JE,S,S3,S3 6,S3 6J,1,1,dog
11,L2 3AJ,L,L2,L2 3,L2 3A,1,1,dog
12,L9 5NB,L,L9,L9 5,L9 5N,1,1,dog
13,S4 3DE,S,S4,S4 3,S4 3D,1,1,dog
14,S3 7ZU,S,S3,S3 7,S3 7Z,1,1,dog
15,S3 1WG,S,S3,S3 1,S3 1W,1,1,dog
16,S4 8WP,S,S4,S4 8,S4 8W,1,1,dog
17,L1 7FR,L,L1,L1 7,L1 7F,1,1,dog
18,L8 0AR,L,L8,L8 0,L8 0A,1,2,cat
19,S9 0SQ,S,S9,S9 0,S9 0S,1,2,cat
20,S8 6PW,S,S8,S8 6,S8 6P,1,2,cat
21,L6 7QS,L,L6,L6 7,L6 7Q,1,2,cat
22,L5 1LH,L,L5,L5 1,L5 1L,1,2,cat
23,L6 0SU,L,L6,L6 0,L6 0S,1,2,cat
24,S2 1ZF,S,S2,S2 1,S2 1Z,1,2,cat
25,S3 8AR,S,S3,S3 8,S3 8A,1,2,cat
26,L3 9NU,L,L3,L3 9,L3 9N,1,2,cat
27,S5 4LZ,S,S5,S5 4,S5 4L,1,2,cat
28,L4 2HW,L,L4,L4 2,L4 2H,1,2,cat
29,S9 2ES,S,S9,S9 2,S9 2E,1,2,cat
30,L3 5LZ,L,L3,L3 5,L3 5L,1,3,mouse
31,L5 6QA,L,L5,L5 6,L5 6Q,1,3,mouse
32,S8 6ZB,S,S8,S8 6,S8 6Z,1,3,mouse
33,S4 8RU,S,S4,S4 8,S4 8R,1,3,mouse
34,L7 9NG,L,L7,L7 9,L7 9N,1,3,mouse
35,L5 1QX,L,L5,L5 1,L5 1Q,1,3,mouse
36,S2 7JN,S,S2,S2 7,S2 7J,1,3,mouse
37,S5 3SP,S,S5,S5 3,S5 3S,1,3,mouse
38,S5 1UL,S,S5,S5 1,S5 1U,1,3,mouse
39,L5 6UN,L,L5,L5 6,L5 6U,1,3,mouse
40,S7 9PL,S,S7,S7 9,S7 9P,1,3,mouse
41,S5 1AB,S,S5,S5 1,S5 1A,1,3,mouse
42,S6 3TB,S,S6,S6 3,S6 3T,1,3,mouse
43,S8 2UZ,S,S8,S8 2,S8 2U,1,3,mouse
44,S5 9GU,S,S5,S5 9,S5 9G,1,3,mouse
45,L1 1DN,L,L1,L1 1,L1 1D,1,3,mouse
46,L6 8YZ,L,L6,L6 8,L6 8Y,1,3,mouse
47,S6 6GB,S,S6,S6 6,S6 6G,0,3,mouse
48,L3 7BD,L,L3,L3 7,L3 7B,0,3,mouse
49,L1 2JF,L,L1,L1 2,L1 2J,0,3,mouse
50,L3 8GP,L,L3,L3 8,L3 8G,0,3,mouse
51,S2 4PD,S,S2,S2 4,S2 4P,0,3,mouse
52,L5 2XY,L,L5,L5 2,L5 2X,0,3,mouse
53,L4 4DF,L,L4,L4 4,L4 4D,0,3,mouse
54,S6 0QZ,S,S6,S6 0,S6 0Q,0,3,mouse
55,S9 4DA,S,S9,S9 4,S9 4D,0,3,mouse
56,L4 1RZ,L,L4,L4 1,L4 1R,0,3,mouse
57,L1 1YX,L,L1,L1 1,L1 1Y,0,3,mouse
58,L4 8JF,L,L4,L4 8,L4 8J,0,3,mouse
59,L1 9SY,L,L1,L1 9,L1 9S,0,3,mouse
60,S7 1DS,S,S7,S7 1,S7 1D,0,3,mouse
61,S2 3SB,S,S2,S2 3,S2 3S,0,3,mouse
62,S5 5EY,S,S5,S5 5,S5 5E,0,3,mouse
63,L3 6SP,L,L3,L3 6,L3 6S,0,3,mouse
64,S6 9LE,S,S6,S6 9,S6 9L,0,3,mouse
65,S7 6GE,S,S7,S7 6,S7 6G,0,3,mouse
66,S3 2XQ,S,S3,S3 2,S3 2X,0,4,rabbit
67,L6 7RD,L,L6,L6 7,L6 7R,0,4,rabbit
68,L4 5TB,L,L4,L4 5,L4 5T,0,4,rabbit
69,S1 9ZY,S,S1,S1 9,S1 9Z,0,4,rabbit
70,L8 3QT,L,L8,L8 3,L8 3Q,0,4,rabbit
71,S8 1SB,S,S8,S8 1,S8 1S,0,4,rabbit
72,L8 8PD,L,L8,L8 8,L8 8P,0,4,rabbit
73,S8 0YX,S,S8,S8 0,S8 0Y,0,4,rabbit
74,S4 9QH,S,S4,S4 9,S4 9Q,0,4,rabbit
75,S6 4XJ,S,S6,S6 4,S6 4X,0,4,rabbit
76,L8 6YG,L,L8,L8 6,L8 6Y,0,4,rabbit
77,L7 7SP,L,L7,L7 7,L7 7S,0,4,rabbit
78,L5 6TW,L,L5,L5 6,L5 6T,0,4,rabbit
79,S5 7YX,S,S5,S5 7,S5 7Y,0,4,rabbit
80,L1 4HG,L,L1,L1 4,L1 4H,0,5,hamster
81,L6 7DB,L,L6,L6 7,L6 7D,0,5,hamster
82,S6 2UA,S,S6,S6 2,S6 2U,0,5,hamster
83,L9 4PJ,L,L9,L9 4,L9 4P,0,5,hamster
84,L4 5DF,L,L4,L4 5,L4 5D,0,5,hamster
85,L7 7UY,L,L7,L7 7,L7 7U,0,5,hamster
86,S1 0FX,S,S1,S1 0,S1 0F,0,5,hamster
87,S7 5RY,S,S7,S7 5,S7 5R,0,5,hamster
88,S8 1YS,S,S8,S8 1,S8 1Y,0,5,hamster
89,S4 2HB,S,S4,S4 2,S4 2H,0,5,hamster
90,S6 6AZ,S,S6,S6 6,S6 6A,0,5,hamster
91,L8 5YG,L,L8,L8 5,L8 5Y,0,5,hamster
92,L1 5JW,L,L1,L1 5,L1 5J,0,5,hamster
93,S5 8NP,S,S5,S5 8,S5 8N,0,5,hamster
94,S5 5RS,S,S5,S5 5,S5 5R,0,5,hamster
95,L1 9SZ,L,L1,L1 9,L1 9S,0,5,hamster
96,L2 7ZH,L,L2,L2 7,L2 7Z,0,5,hamster
97,L2 4RR,L,L2,L2 4,L2 4R,0,5,hamster
98,S8 3EP,S,S8,S8 3,S8 3E,0,5,hamster
99,L4 6ND,L,L4,L4 6,L4 6N,0,5,hamster